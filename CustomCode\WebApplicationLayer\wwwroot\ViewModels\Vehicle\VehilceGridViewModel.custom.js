﻿(function () {
    // This is the vehicle grid in main vehicles list page
    FleetXQ.Web.ViewModels.VehilceGridViewModelCustom = function (viewmodel) {
        var self = this;
        this.viewmodel = viewmodel;

        this.initialize = function () {
            // Set base filter to exclude deleted records
            self.viewmodel.baseFilterPredicate = "DeletedAtUtc == null";
            self.viewmodel.baseFilterParameters = null;
            self.viewmodel.baseFilterParametersCount = 0;

            // Override the AddNewVehicleNewWindowCommand to match the button binding
            self.viewmodel.AddNewVehicleNewWindow = function () {
                // Store filter values in sessionStorage
                var filterData = {
                    customerId: self.viewmodel.VehicleFilterViewModel.filterData.fields.CustomerValue()?.value?.Data?.Id(),
                    siteId: self.viewmodel.VehicleFilterViewModel.filterData.fields.SiteValue()?.value?.Data?.Id(),
                    departmentId: self.viewmodel.VehicleFilterViewModel.filterData.fields.DepartmentValue()?.value?.Data?.Id()
                };
                sessionStorage.setItem('vehicleFilterValues', JSON.stringify(filterData));

                // Set the hash first
                window.location.hash = "!/Vehicle";

                // Force a reload after a brief delay to ensure hash is set
                window.location.reload();
            };

            // Override the Delete function to show confirmation popup
            self.viewmodel.onConfirmDelete = function (confirm) {
                if (confirm === true) {
                    var configuration = {};
                    configuration.contextId = self.viewmodel.contextId;
                    configuration.successHandler = self.viewmodel.onDeleteSuccess;
                    configuration.errorHandler = self.viewmodel.ShowError;
                    configuration.vehicleId = self.viewmodel.selectedObject().Data.Id();
                    configuration.viewmodel = self.viewmodel;
                    self.viewmodel.setIsBusy(true);
                    ApplicationController.getProxyForComponent("VehicleAPI").SoftDelete(configuration);
                } else {
                    self.viewmodel.setIsBusy(false);
                }
            };

            self.viewmodel.Delete = function () {
                self.viewmodel.setIsBusy(true);
                self.viewmodel.controller.applicationController.showConfirmPopup(
                    self.viewmodel,
                    FleetXQ.Web.Messages.i18n.t('dataSource.confirmDeleteMessage', { entity: FleetXQ.Web.Messages.i18n.t('entities/Vehicle/Vehicle:entityName') }),
                    FleetXQ.Web.Messages.i18n.t('dataSource.confirmDeletePopupTitle'),
                    self.viewmodel.onConfirmDelete,
                    self.viewmodel.contextId
                );
            };

            self.viewmodel.commands.IsExportCommandVisible = function () {
                return ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.HAS_VEHICLES_ACCESS) &&
                    ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_EXPORT_VEHICLE);
            };

            self.viewmodel.commands.IsSyncAllVehicleSettingsCommandVisible = function () {
                return ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.HAS_VEHICLES_ACCESS) &&
                    ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_VIEW_SYNCHRONIZATION) &&
                    !ApplicationController.viewModel.security.currentUserClaims().role?.includes('Customer');
            }

            self.viewmodel.commands.IsUploadLastServiceDateCommandVisible = function () {
                return (ApplicationController.viewModel.security.currentUserClaims().role?.includes('DealerAdmin') == true || ApplicationController.viewModel.security.currentUserClaims().role?.includes('Administrator') == true);
            }

            // Hide Delete button
            self.viewmodel.commands.IsDeleteCommandVisible = function () {
                return self.viewmodel.selectedObjectId() && self.viewmodel.selectedObjectId() !== -1;
            };

            self.viewmodel.commands.IsAddNewVehicleNewWindowCommandVisible = function () {
                return ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.HAS_VEHICLES_ACCESS) &&
                    ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_CREATE_VEHICLE);
            };

            // Add IsAdminActionsColumnVisible to the main view model
            self.viewmodel.IsAdminActionsColumnVisible = function () {
                return ApplicationController.viewModel.security.currentUserClaims().role?.includes('Administrator') == true;
            };

            // // Temporary Fix for Context related issues
            // if (!sessionStorage.getItem('hasReloaded')) {
            //     // Set the flag before reloading
            //     sessionStorage.setItem('hasReloaded', 'true');

            //     // Force a reload after a brief delay to ensure hash is set
            //     window.location.reload();
            // } else {
            //     // Clear the flag for next time
            //     sessionStorage.removeItem('hasReloaded');
            // }
        }

        this.IsShowHireDehirePopupCommandVisible = function () {
            return (self.viewmodel.selectedObjectId() && self.viewmodel.selectedObjectId() !== -1) && (ApplicationController.viewModel.security.currentUserClaims().role?.includes('DealerAdmin') == true || ApplicationController.viewModel.security.currentUserClaims().role?.includes('Administrator') == true);
        }

        this.IsShowRAModuleSwapPopupCommandVisible = function () {
            return (self.viewmodel.selectedObjectId() && self.viewmodel.selectedObjectId() !== -1) &&
                (ApplicationController.viewModel.security.currentUserClaims().CanEditRAModuleSwap == 'True' ||
                    ApplicationController.viewModel.security.currentUserClaims().role?.includes('Administrator') == true ||
                    ApplicationController.viewModel.security.currentUserClaims().role?.includes('DealerAdmin') == true);
        }

        this.IsShowDiagnosticPopupCommandVisible = function () {
            return (self.viewmodel.selectedObjectId() && self.viewmodel.selectedObjectId() !== -1) && ApplicationController.viewModel.security.currentUserClaims().role?.includes('Administrator') == true;
        }

        this.IsVehicleDiagnosticLinkVisible = function () {
            return ApplicationController.viewModel.security.currentUserClaims().role?.includes('Administrator') == true;
        }

        this.IsShowOnDemandSettingsPopupCommandVisible = function () {
            return (self.viewmodel.selectedObjectId() && self.viewmodel.selectedObjectId() !== -1)
                && ApplicationController.viewModel.security.currentUserClaims().role?.includes('Administrator') == true;
        }

        this.IsShowOnDemandAccessPopupCommandVisible = function () {
            return (self.viewmodel.selectedObjectId() && self.viewmodel.selectedObjectId() !== -1)
                && ApplicationController.viewModel.security.currentUserClaims().role?.includes('Administrator') == true;
        }
    }
}());
