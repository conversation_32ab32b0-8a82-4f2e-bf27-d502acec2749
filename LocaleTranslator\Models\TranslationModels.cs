using Newtonsoft.Json;

namespace LocaleTranslator.Models;

public class ClaudeRequest
{
    [JsonProperty("model")]
    public string Model { get; set; } = "claude-3-5-sonnet-20241022";

    [JsonProperty("max_tokens")]
    public int MaxTokens { get; set; } = 8192;

    [JsonProperty("messages")]
    public List<ClaudeMessage> Messages { get; set; } = new();
}

public class ClaudeMessage
{
    [JsonProperty("role")]
    public string Role { get; set; } = string.Empty;

    [JsonProperty("content")]
    public string Content { get; set; } = string.Empty;
}

public class ClaudeResponse
{
    [JsonProperty("content")]
    public List<ClaudeContent> Content { get; set; } = new();

    [JsonProperty("usage")]
    public ClaudeUsage? Usage { get; set; }
}

public class ClaudeContent
{
    [JsonProperty("type")]
    public string Type { get; set; } = string.Empty;

    [JsonProperty("text")]
    public string Text { get; set; } = string.Empty;
}

public class ClaudeUsage
{
    [JsonProperty("input_tokens")]
    public int InputTokens { get; set; }

    [JsonProperty("output_tokens")]
    public int OutputTokens { get; set; }
}

public class TranslationRequest
{
    public string SourcePath { get; set; } = string.Empty;
    public string TargetPath { get; set; } = string.Empty;
    public string TargetLanguage { get; set; } = string.Empty;
    public string JsonContent { get; set; } = string.Empty;
}

public class TranslationResult
{
    public bool Success { get; set; }
    public string TranslatedContent { get; set; } = string.Empty;
    public string ErrorMessage { get; set; } = string.Empty;
    public int InputTokens { get; set; }
    public int OutputTokens { get; set; }
}

public class TranslationOptions
{
    public string SourceLocalesPath { get; set; } = string.Empty;
    public string TargetLocalesPath { get; set; } = string.Empty;
    public string TargetLanguage { get; set; } = string.Empty;
    public string ClaudeApiKey { get; set; } = string.Empty;
    public bool DryRun { get; set; }
    public int MaxConcurrency { get; set; } = 3;
    public int DelayBetweenRequests { get; set; } = 1000; // milliseconds
    public int MaxOutputTokens { get; set; } = 8192;
    public bool DebugMode { get; set; } = false;
}

public class TranslationStatus
{
    public int TotalSourceFiles { get; set; }
    public int TotalTargetFiles { get; set; }
    public List<string> MissingFiles { get; set; } = new();
    public List<string> ExtraFiles { get; set; } = new();
    public List<FileKeyDifference> FilesWithKeyDifferences { get; set; } = new();
    public int TotalMissingKeys { get; set; }
    public int TotalExtraKeys { get; set; }
}

public class FileKeyDifference
{
    public string FilePath { get; set; } = string.Empty;
    public List<string> MissingKeys { get; set; } = new();
    public List<string> ExtraKeys { get; set; } = new();
    public int MissingKeysCount => MissingKeys.Count;
    public int ExtraKeysCount => ExtraKeys.Count;
    public bool HasDifferences => MissingKeys.Count > 0 || ExtraKeys.Count > 0;
}