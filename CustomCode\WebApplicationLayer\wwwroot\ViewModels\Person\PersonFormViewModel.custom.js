﻿(function () {
    //
    FleetXQ.Web.ViewModels.PersonFormViewModelCustom = function (viewmodel) {
        var self = this;
        this.viewmodel = viewmodel;
        self.IsCreateNewCommandVisible = ko.pureComputed(function () {
            return false;
        });

        // Add logging wrapper
        function logDebug(action, details) {
            console.log(`[PersonForm] ${action}:`, details);
        }

        this.cleanupState = function () {
            logDebug('Starting cleanup', { contextId: self.viewmodel.contextId });

            // Don't clear Person SiteId from sessionStorage during cleanup
            // This allows the SiteId to persist for GOUserDepartmentForm
            logDebug('Keeping Person SiteId in sessionStorage during cleanup');

            // Refresh grid visibility after cleanup
            if (self.viewmodel.StatusData.IsGOUser_GOUserDepartmentItemsVisible) {
                self.viewmodel.StatusData.IsGOUser_GOUserDepartmentItemsVisible.notifySubscribers();
            }

            // Reset main object
            var newPerson = new FleetXQ.Web.Model.DataObjects.PersonObject();
            newPerson.ObjectsDataSet = self.viewmodel.controller.ObjectsDataSet;
            newPerson.Data.IsNew(true);

            logDebug('Created new person object', { isNew: newPerson.Data.IsNew() });

            // Reset all sub-form ViewModels
            try {
                if (self.viewmodel.PersonWebsiteAccessFormFormViewModel) {
                    self.viewmodel.PersonWebsiteAccessFormFormViewModel.StatusData.IsVisible(false);
                }
                if (self.viewmodel.DriverFormViewModel) {
                    self.viewmodel.DriverFormViewModel.StatusData.IsVisible(false);
                }
                if (self.viewmodel.PersonVehicleAccessFormFormViewModel) {
                    self.viewmodel.PersonVehicleAccessFormFormViewModel.StatusData.IsVisible(false);
                }
                if (self.viewmodel.SupervisorVehicleAccessFormFormViewModel) {
                    self.viewmodel.SupervisorVehicleAccessFormFormViewModel.StatusData.IsVisible(false);
                }


            } catch (error) {
                console.error('[PersonForm] Error resetting sub-forms:', error);
            }

            // Reset status data
            self.viewmodel.StatusData.IsUIDirty(false);
            self.viewmodel.StatusData.CurrentTabIndex(1);
            self.viewmodel.StatusData.DisplayMode('edit');
            self.viewmodel.StatusData.IsEmpty(true);
            self.viewmodel.StatusData.errorSummary.removeAll();
            self.viewmodel.StatusData.isValid(true);

            // Reset tab visibility
            self.viewmodel.StatusData.IsSupervisorTabVisible(false);
            self.viewmodel.StatusData.IsVehicleTabVisible(false);
            self.viewmodel.StatusData.IsWebsiteTabVisible(false);
            self.viewmodel.StatusData.IsSubscriptionTabVisible(false);

            // Clear site and department validation
            if (newPerson.StatusData) {
                newPerson.StatusData.isSiteValid(true);
                newPerson.StatusData.siteErrorMessage(null);
                newPerson.StatusData.isDepartmentValid(true);
                newPerson.StatusData.departmentErrorMessage(null);
            }

            // Notify sub-forms to cleanup
            try {
                ko.postbox.publish("personFormCleanup");
                logDebug('Published cleanup notification', 'Success');
            } catch (error) {
                console.error('[PersonForm] Error publishing cleanup:', error);
            }

            // Set the new person object after cleanup
            self.viewmodel.PersonObject(newPerson);

            // Force a context refresh
            self.viewmodel.contextId = [self.viewmodel.controller.applicationController.getNextContextId()];

            // Rest of existing cleanup code with logging...
            logDebug('Cleanup complete', 'Success');
        };

        this.onBeforeSave = function () {
            // HACK to fix the following bug:
            // modify driver => add card => save => GO error. dataset corrupted on server side. internalId for card is changed to something not correct. All ok if save is done with driver isdirty

            self.viewmodel.CurrentObject().Data.IsDirty(true);

            return true;
        }

        this.onAfterSave = function () {
            logDebug('After Save triggered', { isNew: self.viewmodel.CurrentObject().Data.IsNew() });

            var personId = self.viewmodel.CurrentObject().Data.Id();

            if (personId && personId !== 'null') {
                // First verify the person exists
                var configuration = {
                    contextId: self.viewmodel.contextId,
                    pks: {
                        Id: personId
                    },
                    successHandler: function (result) {
                        if (result) {
                            // Person exists, safe to navigate
                            self.viewmodel.controller.applicationController.customNavigateToPersonDetail(personId);
                        } else {
                            console.warn('Person not found in database yet, retrying...');
                            // Retry after a delay
                            setTimeout(function () {
                                self.viewmodel.controller.applicationController.customNavigateToPersonDetail(personId);
                            }, 2000); // 2 second delay
                        }
                    },
                    errorHandler: function (error) {
                        console.error('Error verifying person:', error);
                        // Fallback to delayed navigation
                        setTimeout(function () {
                            self.viewmodel.controller.applicationController.customNavigateToPersonDetail(personId);
                        }, 2000);
                    }
                };

                // Attempt to verify person exists
                self.viewmodel.DataStorePerson.LoadObject(configuration);
            } else {
                console.warn('No valid person ID available for redirect');
            }

            // Reset the StatusData to ensure it is not null
            self.viewmodel.StatusData = self.viewmodel.StatusData || {}; // Reinitialize if it's null or undefined

            // If needed, re-trigger the updateVisibility method to re-apply visibility rules
            self.updateVisibility();

            // Ensure the object state is correctly set after save
            self.viewmodel.CurrentObject().Data.IsNew(false);
            self.viewmodel.CurrentObject().Data.IsDirty(false);

            // Note: Edit mode is now handled in OnPersonSaved override
            logDebug('onAfterSave completed', {
                displayMode: self.viewmodel.StatusData.DisplayMode(),
                isBusy: self.viewmodel.StatusData.IsBusy()
            });

            return true;
        }

        // Card visible only if : no access group defined or : Access group defined and user has access to users and to view user card
        this.IsDriver_CardVisible = function () {
            return ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.HAS_USERS_ACCESS) &&
                ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_VIEW_USER_CARD);
        };

        // licenses visible only if : no access group defined or : Access group defined and user has access to users and to view user license
        this.IsDriverVisible = function () {
            return ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.HAS_USERS_ACCESS) &&
                ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_VIEW_USERS);
        };
        // can edit user
        this.IsModifyCommandVisible = function () {
            return (self.viewmodel.StatusData.DisplayMode() == 'view' && !self.viewmodel.StatusData.IsEmpty() && self.viewmodel.DataStore && self.viewmodel.DataStore.CheckAuthorizationForEntityAndMethod('save'))
                && (ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.HAS_USERS_ACCESS) &&
                    ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_EDIT_USER));
        };

        // Helper function to check if user has edit permission
        function hasEditPermission() {
            return ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.HAS_USERS_ACCESS) &&
                ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_EDIT_USER);
        }

        // Validation functions for site and department (save-time only)
        this.validateSiteAndDepartment = function () {
            var personObject = self.viewmodel.CurrentObject();
            if (!personObject) return true;

            var isValid = true;
            var errorMessages = [];

            // Validate Site
            if (!personObject.Data.SiteId() || personObject.Data.SiteId() === null) {
                personObject.StatusData.isSiteValid(false);
                personObject.StatusData.siteErrorMessage('Site is required');
                isValid = false;
                errorMessages.push('Site is required');
            } else {
                personObject.StatusData.isSiteValid(true);
                personObject.StatusData.siteErrorMessage(null);
            }

            // Validate Department
            if (!personObject.Data.DepartmentId() || personObject.Data.DepartmentId() === null) {
                personObject.StatusData.isDepartmentValid(false);
                personObject.StatusData.departmentErrorMessage('Department is required');
                isValid = false;
                errorMessages.push('Department is required');
            } else {
                personObject.StatusData.isDepartmentValid(true);
                personObject.StatusData.departmentErrorMessage(null);
            }

            // Update form validation state only if validation fails
            if (!isValid) {
                self.viewmodel.StatusData.isValid(false);
                // Add errors to form error summary
                self.viewmodel.StatusData.errorSummary.removeAll();
                errorMessages.forEach(function (message) {
                    self.viewmodel.StatusData.errorSummary.push(message);
                });
            }

            logDebug('Save-time Site/Department validation completed', {
                isValid: isValid,
                errorMessages: errorMessages,
                siteId: personObject.Data.SiteId(),
                departmentId: personObject.Data.DepartmentId()
            });

            return isValid;
        };


        this.updateVehicleAccessVisibility = function () {
            // vehicle access visible only if : no access group defined or : Access group defined and user has access to users and to view vehicle access
            var accessGroupVisibility = ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.HAS_USERS_ACCESS) &&
                ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_VIEW_VEHICLE_ACCESS);

            if (self.viewmodel.CurrentObject().getDriver() && self.viewmodel.CurrentObject().getDriver().Data.Card() && self.viewmodel.CurrentObject().Data.IsActiveDriver() && !self.viewmodel.CurrentObject().Data.OnDemand()) {
                self.viewmodel.PersonVehicleAccessFormFormViewModel.StatusData.IsVisible(true && accessGroupVisibility);
            }
            else {
                self.viewmodel.PersonVehicleAccessFormFormViewModel.StatusData.IsVisible(false);
            }
        };

        this.updateVisibility = function () {
            var vehiculeTabEmpty = ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.HAS_USERS_ACCESS) &&
                !ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_VIEW_USER_CARD) &&
                !ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_VIEW_USER_LICENSE) &&
                !ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_VIEW_VEHICLE_ACCESS);

            var supervisorTabEmpty = ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.HAS_USERS_ACCESS) &&
                !ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_VIEW_SUPERVISOR_ACCESS);

            var websiteTabEmpty = ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.HAS_USERS_ACCESS) &&
                !ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_VIEW_WEBSITE_ACCESS);

            var subscriptionTabEmpty = ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.HAS_USERS_ACCESS) &&
                !ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_VIEW_REPORT_SUBSCRIPTION) &&
                !ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_VIEW_ALERTS);

            // Check if person has GoUser using getGOUser() method
            var hasGoUser = false;
            if (self.viewmodel.CurrentObject() && typeof self.viewmodel.CurrentObject().getGOUser === 'function') {
                var goUser = self.viewmodel.CurrentObject().getGOUser();
                hasGoUser = !!(goUser && goUser.Data);
            }
            console.log('GoUser check in updateVisibility', { hasGoUser: hasGoUser });

            // Handle nullable IsActiveDriver property
            // var isActiveDriver = self.viewmodel.CurrentObject().Data.IsActiveDriver();
            // isActiveDriver = (isActiveDriver === undefined || isActiveDriver === null) ? false : isActiveDriver;

            if (self.viewmodel.CurrentObject().Data.IsNew()) {
                // Hide all tabs if the object is new
                self.viewmodel.StatusData.IsSupervisorTabVisible(false);
                self.viewmodel.StatusData.IsVehicleTabVisible(false);
                self.viewmodel.StatusData.IsWebsiteTabVisible(false);
                self.viewmodel.StatusData.IsSubscriptionTabVisible(false);
            } else if (self.isEditMode()) {
                // Show tabs in edit mode only if the user has the appropriate role/access
                self.viewmodel.StatusData.IsSupervisorTabVisible(self.viewmodel.CurrentObject().Data.Supervisor() && !supervisorTabEmpty);
                self.viewmodel.StatusData.IsVehicleTabVisible(self.viewmodel.CurrentObject().Data.IsDriver() && self.viewmodel.CurrentObject().Data.IsActiveDriver() && !vehiculeTabEmpty);
                self.viewmodel.StatusData.IsWebsiteTabVisible(self.viewmodel.CurrentObject().Data.WebSiteAccess() && !websiteTabEmpty);
                self.viewmodel.StatusData.IsSubscriptionTabVisible(self.viewmodel.CurrentObject().Data.WebSiteAccess() && hasGoUser && !subscriptionTabEmpty);
            } else {
                // Apply original logic when not in edit mode
                self.viewmodel.StatusData.IsSupervisorTabVisible(self.viewmodel.CurrentObject().Data.Supervisor() && !supervisorTabEmpty);
                self.viewmodel.StatusData.IsVehicleTabVisible(self.viewmodel.CurrentObject().Data.IsDriver() && self.viewmodel.CurrentObject().Data.IsActiveDriver() && !vehiculeTabEmpty);
                self.viewmodel.StatusData.IsWebsiteTabVisible(self.viewmodel.CurrentObject().Data.WebSiteAccess() && !websiteTabEmpty);
                self.viewmodel.StatusData.IsSubscriptionTabVisible(self.viewmodel.CurrentObject().Data.WebSiteAccess() && hasGoUser && !subscriptionTabEmpty);
            }

            self.updateVehicleAccessVisibility();

            var canUnlockVehicle = self.viewmodel.CurrentObject().Data.CanUnlockVehicle();
            var normalDriverAccess = self.viewmodel.CurrentObject().Data.NormalDriverAccess();
            var vorActivateDeactivate = self.viewmodel.CurrentObject().Data.VORActivateDeactivate();

            self.viewmodel.SupervisorVehicleAccessFormFormViewModel.StatusData.IsVisible(canUnlockVehicle || normalDriverAccess || vorActivateDeactivate);
        }

        // Computed observable to check if the current mode is 'edit'
        self.isEditMode = ko.pureComputed(function () {
            return self.viewmodel.StatusData.DisplayMode() === 'edit';
        });

        // Subscribe to changes
        self.viewmodel.StatusData.DisplayMode.subscribe(self.updateVisibility);

        this.initialize = function () {
            // Add this line at the beginning:
            self.viewmodel.DataStorePerson = new FleetXQ.Web.Model.DataStores.DataStore(new FleetXQ.Web.Model.DataSets.ObjectsDataSet(), 'Person');

            // Add computed observable for GOUserDepartmentGrid visibility based on WebsiteAccessLevel
            self.viewmodel.StatusData.IsGOUser_GOUserDepartmentItemsVisible = ko.pureComputed(function () {
                // Check if the person has a GOUser and if the WebsiteAccessLevel is MultiDepartment
                var currentObject = self.viewmodel.CurrentObject();
                if (currentObject && currentObject.getGOUser) {
                    var goUser = currentObject.getGOUser();
                    if (goUser && goUser.Data && goUser.Data.WebsiteAccessLevel) {
                        var websiteAccessLevel = goUser.Data.WebsiteAccessLevel();
                        // MultiDepartment = 4 according to the enum
                        var isMultiDepartment = websiteAccessLevel === 4;

                        console.log('PersonFormViewModelCustom: GOUserDepartmentGrid visibility check', {
                            websiteAccessLevel: websiteAccessLevel,
                            isMultiDepartment: isMultiDepartment,
                            goUserId: goUser.Data.Id(),
                            personId: currentObject.Data.Id()
                        });

                        return isMultiDepartment;
                    }
                }

                console.log('PersonFormViewModelCustom: GOUserDepartmentGrid visibility - no GOUser or WebsiteAccessLevel found');
                return false;
            });

            // Store Person's SiteId in sessionStorage for GOUserDepartmentForm filtering
            self.viewmodel.subscriptions.push(self.viewmodel.CurrentObject.subscribe(function (newValue) {
                if (newValue && newValue.Data && newValue.Data.SiteId) {
                    var siteId = newValue.Data.SiteId();
                    if (siteId && siteId !== 'null' && siteId !== 'undefined') {
                        sessionStorage.setItem('personSiteId', siteId);
                        logDebug('Stored Person SiteId in sessionStorage', { siteId: siteId });
                        console.log('PersonFormViewModelCustom: Stored SiteId in sessionStorage for GOUserDepartmentForm filtering', { siteId: siteId });
                    } else {
                        sessionStorage.removeItem('personSiteId');
                        logDebug('Removed Person SiteId from sessionStorage', 'SiteId was null or undefined');
                        console.log('PersonFormViewModelCustom: Removed SiteId from sessionStorage', 'SiteId was null or undefined');
                    }
                }

                // Refresh grid visibility when Person object changes
                setTimeout(function () {
                    if (self.viewmodel.StatusData.IsGOUser_GOUserDepartmentItemsVisible) {
                        self.viewmodel.StatusData.IsGOUser_GOUserDepartmentItemsVisible.notifySubscribers();
                        console.log('PersonFormViewModelCustom: Refreshed grid visibility after Person object change');
                    }
                }, 100);
            }));

            // Also store SiteId when it changes directly
            if (self.viewmodel.CurrentObject() && self.viewmodel.CurrentObject().Data && self.viewmodel.CurrentObject().Data.SiteId) {
                self.viewmodel.subscriptions.push(self.viewmodel.CurrentObject().Data.SiteId.subscribe(function (newSiteId) {
                    if (newSiteId && newSiteId !== 'null' && newSiteId !== 'undefined') {
                        sessionStorage.setItem('personSiteId', newSiteId);
                        logDebug('Updated Person SiteId in sessionStorage', { siteId: newSiteId });
                        console.log('PersonFormViewModelCustom: Updated SiteId in sessionStorage', { siteId: newSiteId });
                    } else {
                        sessionStorage.removeItem('personSiteId');
                        logDebug('Removed Person SiteId from sessionStorage', 'SiteId was null or undefined');
                        console.log('PersonFormViewModelCustom: Removed SiteId from sessionStorage', 'SiteId was null or undefined');
                    }
                }));
            }

            // Store current SiteId immediately if it exists
            if (self.viewmodel.CurrentObject() && self.viewmodel.CurrentObject().Data && self.viewmodel.CurrentObject().Data.SiteId) {
                var currentSiteId = self.viewmodel.CurrentObject().Data.SiteId();
                if (currentSiteId && currentSiteId !== 'null' && currentSiteId !== 'undefined') {
                    sessionStorage.setItem('personSiteId', currentSiteId);
                    console.log('PersonFormViewModelCustom: Stored current SiteId in sessionStorage on initialization', { siteId: currentSiteId });
                }
            }

            // Create a global function to get the current Person's SiteId
            window.getCurrentPersonSiteId = function () {
                if (self.viewmodel.CurrentObject() && self.viewmodel.CurrentObject().Data && self.viewmodel.CurrentObject().Data.SiteId) {
                    var siteId = self.viewmodel.CurrentObject().Data.SiteId();
                    if (siteId && siteId !== 'null' && siteId !== 'undefined') {
                        return siteId;
                    }
                }
                return null;
            };

            // Override CancelEditCommand to call Edit instead of CancelEdit
            var originalCancelEditCommand = self.viewmodel.Commands.CancelEditCommand;
            self.viewmodel.Commands.CancelEditCommand = function () {
                logDebug('CancelEditCommand triggered', {
                    isDirty: self.viewmodel.controller.ObjectsDataSet.isContextIdDirty(self.viewmodel.contextId),
                    isNew: self.viewmodel.CurrentObject().Data.IsNew()
                });

                // Check if the object is dirty (has unsaved changes)
                if (self.viewmodel.controller.ObjectsDataSet.isContextIdDirty(self.viewmodel.contextId)) {
                    // Show confirmation dialog for dirty object
                    self.viewmodel.controller.applicationController.showConfirmPopup(
                        self.viewmodel,
                        'You have unsaved changes. Are you sure you want to discard them and reload the person data?',
                        'Discard Changes',
                        function (confirm) {
                            if (confirm) {
                                logDebug('User confirmed discard changes', 'Proceeding with reload');
                                // Reload the person data
                                if (!self.viewmodel.CurrentObject().Data.IsNew()) {
                                    self.viewmodel.LoadPerson(self.viewmodel.CurrentObject());
                                } else {
                                    // For new objects, use CreateNew to safely reinitialize
                                    logDebug('Reinitializing new person object', 'Using CreateNew method');
                                    self.viewmodel.CreateNew();
                                }
                            } else {
                                logDebug('User cancelled discard changes', 'Staying in current state');
                            }
                        },
                        self.viewmodel.contextId
                    );
                } else {
                    // No changes to discard, just reload or reinitialize
                    logDebug('No changes to discard', 'Proceeding with reload/reinitialize');
                    if (!self.viewmodel.CurrentObject().Data.IsNew()) {
                        self.viewmodel.LoadPerson(self.viewmodel.CurrentObject());
                    } else {
                        // For new objects, use CreateNew to safely reinitialize
                        logDebug('Reinitializing new person object', 'Using CreateNew method');
                        self.viewmodel.CreateNew();
                    }
                }
            };

            // Override OnPersonSaved to remove EndEdit() call and stay in edit mode
            var originalOnPersonSaved = self.viewmodel.OnPersonSaved;
            self.viewmodel.OnPersonSaved = function (objectSaved) {
                logDebug('OnPersonSaved triggered', { isNew: objectSaved.Data.IsNew() });

                // Call the original OnPersonSaved logic but without EndEdit
                self.viewmodel.SetPersonObject(objectSaved);

                // Don't call EndEdit() - we want to stay in edit mode
                // self.viewmodel.EndEdit(); // Commented out to prevent switching to view mode

                if (self.viewmodel.StatusData.isPopup()) {
                    var preventRebind = false;
                    if (self.viewmodel.popupCaller && self.viewmodel.popupCaller.CallAfterSaveRelatedEntity) {
                        // The CallAfterSaveRelatedEntity may return true to prevent rebind when closing the popup
                        preventRebind = self.viewmodel.popupCaller.CallAfterSaveRelatedEntity(objectSaved);
                    }
                    self.viewmodel.closePopup(!preventRebind);
                }

                self.viewmodel.Events.PersonSaved(!self.viewmodel.Events.PersonSaved());

                // Ensure busy state is cleared (since we're not calling EndEdit)
                self.viewmodel.setIsBusy(false);
                logDebug('OnPersonSaved completed - staying in edit mode', {
                    displayMode: self.viewmodel.StatusData.DisplayMode(),
                    isBusy: self.viewmodel.StatusData.IsBusy()
                });
            };

            // Override CancelEdit to add comprehensive safety checks
            var originalCancelEdit = self.viewmodel.CancelEdit;
            self.viewmodel.CancelEdit = function (isCommandCall, isCreatePopup) {
                logDebug('CancelEdit triggered with comprehensive safety checks', {
                    isCommandCall: isCommandCall,
                    isCreatePopup: isCreatePopup,
                    hasSavedData: !!self.viewmodel.SavedData,
                    savedDataType: self.viewmodel.SavedData ? typeof self.viewmodel.SavedData : 'null'
                });

                // Comprehensive safety check for SavedData
                if (!self.viewmodel.SavedData || !self.viewmodel.SavedData.Data) {
                    logDebug('SavedData is null or invalid, skipping data restoration', 'Safety check');
                    // Just reset to view mode without data restoration
                    self.viewmodel.StatusData.DisplayMode('view');
                    self.viewmodel.StatusData.IsEmpty(self.viewmodel.StatusData.PreviousIsEmpty);
                    self.viewmodel.controller.ObjectsDataSet.resetContextIdDirty(self.viewmodel.contextId);
                    self.viewmodel.resetValidation();
                    self.viewmodel.Events.CancelEdit(!self.viewmodel.Events.CancelEdit());
                    return;
                }

                // Additional safety check for sub-forms
                try {
                    // Temporarily disable sub-form cancel operations that might cause issues
                    var originalCardCancelEdit = self.viewmodel.CardFormViewModel ? self.viewmodel.CardFormViewModel.CancelEdit : null;
                    var originalDriverCancelEdit = self.viewmodel.DriverFormViewModel ? self.viewmodel.DriverFormViewModel.CancelEdit : null;

                    if (originalCardCancelEdit) {
                        self.viewmodel.CardFormViewModel.CancelEdit = function () {
                            logDebug('Skipping CardFormViewModel CancelEdit due to safety check');
                        };
                    }

                    if (originalDriverCancelEdit) {
                        self.viewmodel.DriverFormViewModel.CancelEdit = function () {
                            logDebug('Skipping DriverFormViewModel CancelEdit due to safety check');
                        };
                    }

                    // Call original CancelEdit
                    originalCancelEdit.call(self.viewmodel, isCommandCall, isCreatePopup);

                    // Restore original functions
                    if (originalCardCancelEdit) {
                        self.viewmodel.CardFormViewModel.CancelEdit = originalCardCancelEdit;
                    }
                    if (originalDriverCancelEdit) {
                        self.viewmodel.DriverFormViewModel.CancelEdit = originalDriverCancelEdit;
                    }
                } catch (error) {
                    logDebug('Error during CancelEdit, falling back to safe mode', { error: error.message });
                    // Fallback to safe mode
                    self.viewmodel.StatusData.DisplayMode('view');
                    self.viewmodel.StatusData.IsEmpty(self.viewmodel.StatusData.PreviousIsEmpty);
                    self.viewmodel.controller.ObjectsDataSet.resetContextIdDirty(self.viewmodel.contextId);
                    self.viewmodel.resetValidation();
                    self.viewmodel.Events.CancelEdit(!self.viewmodel.Events.CancelEdit());
                }
            };

            // Add safety check to BaseDataObject CopyValuesFrom method - more robust approach
            if (FleetXQ.Web.Model.DataObjects.BaseDataObject) {
                var originalCopyValuesFrom = FleetXQ.Web.Model.DataObjects.BaseDataObject.prototype.CopyValuesFrom;
                FleetXQ.Web.Model.DataObjects.BaseDataObject.prototype.CopyValuesFrom = function (sourceObject) {
                    // Add safety check for sourceObject
                    if (!sourceObject || !sourceObject.Data) {
                        logDebug('CopyValuesFrom called with invalid sourceObject', {
                            sourceObject: sourceObject,
                            hasData: sourceObject && !!sourceObject.Data,
                            stack: new Error().stack
                        });
                        return; // Skip copying if sourceObject is invalid
                    }

                    // Call original method if sourceObject is valid
                    originalCopyValuesFrom.call(this, sourceObject);
                };
            }

            // Also add a global safety check for all data objects
            if (window.FleetXQ && FleetXQ.Web && FleetXQ.Web.Model && FleetXQ.Web.Model.DataObjects) {
                // Override CopyValuesFrom for all data objects that might be affected
                var dataObjectTypes = ['LicenseByModelObject', 'DriverObject', 'PersonObject'];
                dataObjectTypes.forEach(function (typeName) {
                    if (FleetXQ.Web.Model.DataObjects[typeName] && FleetXQ.Web.Model.DataObjects[typeName].prototype) {
                        var originalCopyValuesFrom = FleetXQ.Web.Model.DataObjects[typeName].prototype.CopyValuesFrom;
                        FleetXQ.Web.Model.DataObjects[typeName].prototype.CopyValuesFrom = function (sourceObject) {
                            if (!sourceObject || !sourceObject.Data) {
                                logDebug('CopyValuesFrom called with invalid sourceObject for ' + typeName, {
                                    sourceObject: sourceObject,
                                    hasData: sourceObject && !!sourceObject.Data
                                });
                                return;
                            }
                            originalCopyValuesFrom.call(this, sourceObject);
                        };
                    }
                });
            }

            // Temporary Fix for Context related issues
            // if (!sessionStorage.getItem('hasReloaded')) {
            //     // Set the flag before reloading
            //     sessionStorage.setItem('hasReloaded', 'true');

            //     // Force a reload after a brief delay to ensure hash is set
            //     window.location.reload();
            // } else {
            //     // Clear the flag for next time
            //     sessionStorage.removeItem('hasReloaded');
            // }

            logDebug('Initializing PersonForm', { contextId: self.viewmodel.contextId });

            // Set form to edit mode when fully loaded (but not after save operations)
            self.viewmodel.subscriptions.push(self.viewmodel.Events.PersonLoaded.subscribe(function (newValue) {
                if (newValue && !self.viewmodel.CurrentObject().Data.IsNew()) {
                    logDebug('Person loaded, switching to edit mode', 'Success');
                    // Only switch to edit mode if we're not currently in a save operation
                    if (!self.viewmodel.StatusData.IsBusy()) {
                        setTimeout(function () {
                            self.viewmodel.Edit();
                        }, 100); // Small delay to ensure form is fully rendered
                    }
                }
            }));

            // Override the Save function to validate before showing confirmation popup
            var originalSave = self.viewmodel.Save;
            self.viewmodel.Save = function () {
                // Run validation first
                var validationResult = self.validateSiteAndDepartment();

                if (!validationResult) {
                    logDebug('Save blocked due to validation failure', 'Validation failed');
                    // Show error popup instead of confirmation popup
                    self.viewmodel.controller.applicationController.showAlertPopup(
                        self.viewmodel,
                        'Please fix the validation errors before saving.',
                        'Validation Error',
                        null,
                        self.viewmodel.contextId
                    );
                    return;
                }

                // If validation passes, show confirmation popup
                self.viewmodel.controller.applicationController.showConfirmPopup(
                    self.viewmodel,
                    'Are you sure you want to save this Person?',
                    'Save confirmation',
                    function (confirm) {
                        if (confirm) {
                            logDebug('Save confirmed by user', 'Proceeding with save');
                            originalSave.call(self.viewmodel);
                        } else {
                            logDebug('Save cancelled by user', 'Aborting save');
                        }
                    },
                    self.viewmodel.contextId
                );
            };

            // Override the Delete function to show confirmation popup
            self.viewmodel.onConfirmDelete = function (confirm) {
                if (confirm === true) {
                    var configuration = {};
                    configuration.caller = self.viewmodel;
                    configuration.contextId = self.viewmodel.contextId;
                    configuration.successHandler = function (data) {
                        self.viewmodel.onDeleteSuccess(data);
                        // Navigate back to users page after successful deletion
                        window.location.hash = "!/UserManagement";
                    };
                    configuration.errorHandler = self.viewmodel.ShowError;
                    configuration.personId = self.viewmodel.PersonObject().Data.Id();
                    self.viewmodel.setIsBusy(true);
                    self.viewmodel.controller.applicationController.getProxyForComponent("PersonAPI").SoftDelete(configuration);
                } else {
                    self.viewmodel.setIsBusy(false);
                }
            };

            self.viewmodel.Delete = function () {
                self.viewmodel.setIsBusy(true);
                self.viewmodel.controller.applicationController.showConfirmPopup(
                    self.viewmodel,
                    FleetXQ.Web.Messages.i18n.t('dataSource.confirmDeleteMessage', { entity: FleetXQ.Web.Messages.i18n.t('entities/Person/Person:entityName') }),
                    FleetXQ.Web.Messages.i18n.t('dataSource.confirmDeletePopupTitle'),
                    self.viewmodel.onConfirmDelete,
                    self.viewmodel.contextId
                );
            };

            self.updateVisibility();

            // Listen for license deletion events
            ko.postbox.subscribe("licenseDeleted", function () {
                logDebug('License deletion detected', 'Switching to view mode');
                self.viewmodel.Save();
            });

            self.viewmodel.subscriptions.push(self.viewmodel.Events.CancelEdit.subscribe(function (newValue) {
                if (self.viewmodel.CurrentObject().Data.IsNew()) {
                    window.location.hash = "!/UserManagement";
                }
            }));

            self.viewmodel.subscriptions.push(self.viewmodel.Events.PersonLoaded.subscribe(function (newValue) {
                self.updateVisibility();
            }));

            // Update PersonSaved event
            self.viewmodel.Events.PersonSaved.subscribe(function (newValue) {
                self.updateVehicleAccessVisibility();
            });

            self.viewmodel.subscriptions.push(self.viewmodel.CurrentObject.subscribe(function (newValue) {
                self.viewmodel.CurrentObject().Data.Supervisor.subscribe(function (newValue) {
                    if (self.viewmodel.CurrentObject().Data.IsNew()) {
                        self.viewmodel.StatusData.IsSupervisorTabVisible(false);
                        return;
                    }

                    self.viewmodel.StatusData.IsSupervisorTabVisible(newValue);
                });

                self.viewmodel.CurrentObject().Data.IsDriver.subscribe(function (newValue) {
                    if (self.viewmodel.CurrentObject().Data.IsNew()) {
                        self.viewmodel.StatusData.IsVehicleTabVisible(false);
                        self.viewmodel.CurrentObject().Data.IsActiveDriver(newValue);
                        return;
                    }

                    if (newValue == false) {
                        self.viewmodel.CurrentObject().Data.IsActiveDriver(false);
                    }

                    // Update visibility to consider both IsDriver and IsActiveDriver
                    var isActiveDriver = self.viewmodel.CurrentObject().Data.IsActiveDriver();
                    isActiveDriver = (isActiveDriver === undefined || isActiveDriver === null) ? false : isActiveDriver;

                    var vehiculeTabEmpty = ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.HAS_USERS_ACCESS) &&
                        !ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_VIEW_USER_CARD) &&
                        !ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_VIEW_USER_LICENSE) &&
                        !ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_VIEW_VEHICLE_ACCESS);

                    self.viewmodel.StatusData.IsVehicleTabVisible(newValue && isActiveDriver && !vehiculeTabEmpty);
                });

                self.viewmodel.CurrentObject().Data.WebSiteAccess.subscribe(function (newValue) {
                    if (self.viewmodel.CurrentObject().Data.IsNew()) {
                        self.viewmodel.StatusData.IsWebsiteTabVisible(false);
                        return;
                    }

                    self.viewmodel.StatusData.IsWebsiteTabVisible(newValue);
                });

                self.viewmodel.CurrentObject().Data.OnDemand.subscribe(function (newValue) {
                    if (self.viewmodel.CurrentObject().Data.IsNew()) {
                        self.viewmodel.StatusData.VehicleAccess(false);
                        return;
                    }

                    self.viewmodel.CurrentObject().Data.VehicleAccess(!newValue);
                });

                self.viewmodel.CurrentObject().Data.VehicleAccess.subscribe(function (newValue) {
                    self.updateVehicleAccessVisibility();
                });

                self.viewmodel.CurrentObject().Data.CanUnlockVehicle.subscribe(function (newValue) {
                    var normalDriverAccess = self.viewmodel.CurrentObject().Data.NormalDriverAccess();
                    var vorActivateDeactivate = self.viewmodel.CurrentObject().Data.VORActivateDeactivate();

                    self.viewmodel.SupervisorVehicleAccessFormFormViewModel.StatusData.IsVisible(newValue || normalDriverAccess || vorActivateDeactivate);
                });

                self.viewmodel.CurrentObject().Data.NormalDriverAccess.subscribe(function (newValue) {
                    var canUnlockVehicle = self.viewmodel.CurrentObject().Data.CanUnlockVehicle();
                    var vorActivateDeactivate = self.viewmodel.CurrentObject().Data.VORActivateDeactivate();

                    self.viewmodel.SupervisorVehicleAccessFormFormViewModel.StatusData.IsVisible(canUnlockVehicle || newValue || vorActivateDeactivate);
                });

                self.viewmodel.CurrentObject().Data.VORActivateDeactivate.subscribe(function (newValue) {
                    var canUnlockVehicle = self.viewmodel.CurrentObject().Data.CanUnlockVehicle();
                    var normalDriverAccess = self.viewmodel.CurrentObject().Data.NormalDriverAccess();

                    self.viewmodel.SupervisorVehicleAccessFormFormViewModel.StatusData.IsVisible(canUnlockVehicle || normalDriverAccess || newValue);
                });

                // Add subscription for IsActiveDriver
                self.viewmodel.CurrentObject().Data.IsActiveDriver.subscribe(function (newValue) {
                    if (self.viewmodel.CurrentObject().Data.IsNew()) {
                        self.viewmodel.StatusData.IsVehicleTabVisible(false);
                        return;
                    }

                    // Handle nullable value
                    newValue = (newValue === undefined || newValue === null) ? false : newValue;

                    // Update visibility based on both IsDriver and IsActiveDriver
                    var isDriver = self.viewmodel.CurrentObject().Data.IsDriver();
                    var vehiculeTabEmpty = ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.HAS_USERS_ACCESS) &&
                        !ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_VIEW_USER_CARD) &&
                        !ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_VIEW_USER_LICENSE) &&
                        !ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_VIEW_VEHICLE_ACCESS);

                    self.viewmodel.StatusData.IsVehicleTabVisible(isDriver && newValue && !vehiculeTabEmpty);
                });
            }));

            // Add error boundary to subscriptions
            self.viewmodel.subscriptions.push(ko.postbox.subscribe("navigationOccurred", function () {
                try {
                    logDebug('Navigation occurred', 'Cleaning state');
                    self.cleanupState();
                } catch (error) {
                    console.error('[PersonForm] Error during navigation cleanup:', error);
                }
            }));

            // Subscribe to GOUser changes to update grid visibility
            self.viewmodel.subscriptions.push(self.viewmodel.CurrentObject.subscribe(function (newValue) {
                if (newValue && newValue.getGOUser) {
                    var goUser = newValue.getGOUser();
                    if (goUser && goUser.Data && goUser.Data.WebsiteAccessLevel) {
                        // Subscribe to WebsiteAccessLevel changes
                        goUser.Data.WebsiteAccessLevel.subscribe(function (newLevel) {
                            console.log('PersonFormViewModelCustom: WebsiteAccessLevel changed', {
                                newLevel: newLevel,
                                isMultiDepartment: newLevel === 4
                            });
                            // The computed observable will automatically update
                        });
                    }
                }
            }));

            // Subscribe to GOUser changes to update grid visibility
            self.viewmodel.subscriptions.push(self.viewmodel.CurrentObject.subscribe(function (newValue) {
                if (newValue && newValue.getGOUser) {
                    var goUser = newValue.getGOUser();
                    if (goUser && goUser.Data && goUser.Data.WebsiteAccessLevel) {
                        // Subscribe to WebsiteAccessLevel changes
                        goUser.Data.WebsiteAccessLevel.subscribe(function (newLevel) {
                            console.log('PersonFormViewModelCustom: WebsiteAccessLevel changed', {
                                newLevel: newLevel,
                                isMultiDepartment: newLevel === 4
                            });
                            // The computed observable will automatically update
                        });
                    }
                }
            }));

            // Subscribe to GOUser saved events to refresh visibility
            self.viewmodel.subscriptions.push(ko.postbox.subscribe("GOUser.ChangedOnGrid", function (payload) {
                console.log('PersonFormViewModelCustom: GOUser changed event received', payload);
                // Force the computed observable to re-evaluate by notifying subscribers
                if (self.viewmodel.StatusData.IsGOUser_GOUserDepartmentItemsVisible) {
                    self.viewmodel.StatusData.IsGOUser_GOUserDepartmentItemsVisible.notifySubscribers();
                }
            }));

            // Clean up sessionStorage when form is released
            var originalRelease = self.viewmodel.release;
            self.viewmodel.release = function () {
                // Don't clear Person SiteId from sessionStorage on form release
                // This allows the SiteId to persist for GOUserDepartmentForm
                logDebug('Keeping Person SiteId in sessionStorage on form release');

                // Call original release function
                if (originalRelease) {
                    originalRelease.call(self.viewmodel);
                }
            };

            // Add initialization for EmailGroupsItemsGridViewModel
            if (self.viewmodel.EmailGroupsItemsGridViewModel) {
                self.viewmodel.EmailGroupsItemsGridViewModel.AddNewCommandInitActions = [
                    function (newobject) {
                        // Get the current person's customer
                        var currentPerson = self.viewmodel.PersonObject();
                        if (currentPerson && currentPerson.getCustomer()) {
                            var customer = currentPerson.getCustomer();
                            // Set the customer for the new email group
                            newobject.setCustomer(customer);
                        }
                    }
                ];
            }
        };
    }
}());
