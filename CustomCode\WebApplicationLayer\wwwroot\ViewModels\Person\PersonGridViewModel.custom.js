﻿(function () {
    //
    FleetXQ.Web.ViewModels.PersonGridViewModelCustom = function (viewmodel) {
        var self = this;
        this.viewmodel = viewmodel;

        this.initialize = function () {
            // Set base filter to exclude deleted records
            self.viewmodel.baseFilterPredicate = "DeletedAtUtc == null";
            self.viewmodel.baseFilterParameters = null;
            self.viewmodel.baseFilterParametersCount = 0;

            // Override the AddNewUserNewWindow function
            self.viewmodel.AddNewUserNewWindow = function () {
                if (self.viewmodel.PersonFilterViewModel) {
                    var filterData = {
                        customerId: self.viewmodel.PersonFilterViewModel.filterData.fields.CustomerValue()?.value?.Data?.Id(),
                        siteId: self.viewmodel.PersonFilterViewModel.filterData.fields.SiteValue()?.value?.Data?.Id(),
                        departmentId: self.viewmodel.PersonFilterViewModel.filterData.fields.DepartmentValue()?.value?.Data?.Id()
                    };
                    sessionStorage.setItem('personFilterValues', JSON.stringify(filterData));
                }

                // Set the hash first
                window.location.hash = "!/UserManagement/UserDetail";

                // Force a reload after a brief delay to ensure hash is set
                setTimeout(function () {
                    window.location.reload();
                }, 100);
            };

            self.viewmodel.commands.IsDeleteCommandVisible = function () {
                return self.viewmodel.selectedObjectId() && self.viewmodel.selectedObjectId() !== -1;
            };

            self.viewmodel.commands.IsExportCommandVisible = function () {
                return ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.HAS_USERS_ACCESS) &&
                    ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_EXPORT_USERS);
            };

            self.viewmodel.commands.IsAddNewUserNewWindowCommandVisible = function () {
                return ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.HAS_USERS_ACCESS) &&
                    ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_CREATE_USER);
            };

            // Override the Delete function to show confirmation popup
            self.viewmodel.onConfirmDelete = function (confirm) {
                if (confirm === true) {
                    var configuration = {};
                    configuration.contextId = self.viewmodel.contextId;
                    configuration.successHandler = self.viewmodel.onDeleteSuccess;
                    configuration.errorHandler = self.viewmodel.ShowError;
                    configuration.personId = self.viewmodel.selectedObject().Data.Id();
                    configuration.viewmodel = self.viewmodel;
                    self.viewmodel.setIsBusy(true);
                    ApplicationController.getProxyForComponent("PersonAPI").SoftDelete(configuration);
                } else {
                    self.viewmodel.setIsBusy(false);
                }
            };

            self.viewmodel.Delete = function () {
                self.viewmodel.setIsBusy(true);
                self.viewmodel.controller.applicationController.showConfirmPopup(
                    self.viewmodel,
                    FleetXQ.Web.Messages.i18n.t('dataSource.confirmDeleteMessage', { entity: FleetXQ.Web.Messages.i18n.t('entities/Person/Person:entityName') }),
                    FleetXQ.Web.Messages.i18n.t('dataSource.confirmDeletePopupTitle'),
                    self.viewmodel.onConfirmDelete,
                    self.viewmodel.contextId
                );
            };

            // // Temporary Fix for Context related issues
            // if (!sessionStorage.getItem('hasReloaded')) {
            //     // Set the flag before reloading
            //     sessionStorage.setItem('hasReloaded', 'true');

            //     // Force a reload after a brief delay to ensure hash is set
            //     window.location.reload();
            // } else {
            //     // Clear the flag for next time
            //     sessionStorage.removeItem('hasReloaded');
            // }
        }

        this.IsShowCopyDriverAccessSettingsPopupCommandVisible = function () {
            var selectedPerson = self.viewmodel.selectedObject();

            if (selectedPerson == null) {
                return false;
            }

            var driver = selectedPerson.getDriver();

            if (driver == null) {
                return false;
            }

            var card = driver.Data.Card();

            if (card == null) {
                return false;
            }

            return card.Data.Active();
        };
    }
}());
