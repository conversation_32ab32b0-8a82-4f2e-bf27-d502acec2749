using System.CommandLine;
using LocaleTranslator.Models;
using LocaleTranslator.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;

namespace LocaleTranslator;

class Program
{
    static async Task<int> Main(string[] args)
    {
        // Load configuration
        var config = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
            .Build();

        var rootCommand = new RootCommand("Automated translation tool for JSON locale files using Claude AI")
        {
            CreateTranslateCommand(config),
            CreateStatusCommand(config)
        };

        return await rootCommand.InvokeAsync(args);
    }

    static Command CreateTranslateCommand(IConfiguration config)
    {
        var sourceOption = new Option<string>(
            aliases: new[] { "--source", "-s" },
            description: "Source locales directory path (e.g., './locales/default')")
        { IsRequired = true };

        var targetOption = new Option<string>(
            aliases: new[] { "--target", "-t" },
            description: "Target locales directory path (e.g., './locales/french')")
        { IsRequired = true };

        var languageOption = new Option<string>(
            aliases: new[] { "--language", "-l" },
            description: "Target language (e.g., 'French', 'Spanish', 'German')")
        { IsRequired = true };

        var verboseOption = new Option<bool>(
            aliases: new[] { "--verbose", "-v" },
            description: "Enable verbose logging",
            getDefaultValue: () => false);

        var debugOption = new Option<bool>(
            aliases: new[] { "--debug" },
            description: "Enable debug mode (saves problematic responses to debug files)",
            getDefaultValue: () => false);

        var translateCommand = new Command("translate", "Translate JSON locale files")
        {
            sourceOption,
            targetOption,
            languageOption,
            verboseOption,
            debugOption
        };

        translateCommand.SetHandler(async (context) =>
        {
            var source = context.ParseResult.GetValueForOption(sourceOption);
            var target = context.ParseResult.GetValueForOption(targetOption);
            var language = context.ParseResult.GetValueForOption(languageOption);
            var verbose = context.ParseResult.GetValueForOption(verboseOption);
            var debug = context.ParseResult.GetValueForOption(debugOption);

            // Get API key from parameter, environment, or config
            var apiKey = Environment.GetEnvironmentVariable("CLAUDE_API_KEY");
            if (string.IsNullOrEmpty(apiKey))
            {
                apiKey = config["Claude:ApiKey"];
            }
            if (string.IsNullOrEmpty(apiKey))
            {
                Console.WriteLine("Error: Claude API key is required. Provide it via CLAUDE_API_KEY environment variable, or appsettings.json config file.");
                Environment.Exit(1);
                return;
            }

            var options = new TranslationOptions
            {
                SourceLocalesPath = source,
                TargetLocalesPath = target,
                TargetLanguage = language,
                ClaudeApiKey = apiKey,
                DryRun = false,
                DelayBetweenRequests = 1000,
                MaxOutputTokens = 8192,
                DebugMode = debug
            };

            await RunTranslationAsync(options, verbose, debug, config);
        });

        return translateCommand;
    }

    static Command CreateStatusCommand(IConfiguration config)
    {
        var sourceOption = new Option<string>(
            aliases: new[] { "--source", "-s" },
            description: "Source locales directory path (e.g., './locales/default')")
        { IsRequired = true };

        var targetOption = new Option<string>(
            aliases: new[] { "--target", "-t" },
            description: "Target locales directory path (e.g., './locales/french')")
        { IsRequired = true };

        var languageOption = new Option<string>(
            aliases: new[] { "--language", "-l" },
            description: "Target language (e.g., 'French', 'Spanish', 'German')")
        { IsRequired = true };

        var statusCommand = new Command("status", "Check translation status by comparing target language folder with default folder")
        {
            sourceOption,
            targetOption,
            languageOption
        };

        statusCommand.SetHandler(async (context) =>
        {
            var source = context.ParseResult.GetValueForOption(sourceOption);
            var target = context.ParseResult.GetValueForOption(targetOption);
            var language = context.ParseResult.GetValueForOption(languageOption);

            var options = new TranslationOptions
            {
                SourceLocalesPath = source,
                TargetLocalesPath = target,
                TargetLanguage = language
            };

            await CheckTranslationStatusAsync(options, config);
        });

        return statusCommand;
    }

    static async Task RunTranslationAsync(TranslationOptions options, bool verbose, bool debug, IConfiguration config)
    {
        // Setup DI container
        var services = new ServiceCollection();

        // Configure logging
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            if (verbose)
            {
                builder.SetMinimumLevel(LogLevel.Debug);
            }
            else
            {
                builder.SetMinimumLevel(LogLevel.Information);
            }
        });

        // Register HTTP client
        services.AddHttpClient<ClaudeTranslationService>();

        // Register services
        services.AddSingleton<ClaudeTranslationService>();
        services.AddSingleton<FileProcessingService>(provider =>
            new FileProcessingService(
                provider.GetService<ClaudeTranslationService>(),
                provider.GetRequiredService<ILogger<FileProcessingService>>()));

        // Build service provider
        using var serviceProvider = services.BuildServiceProvider();
        var logger = serviceProvider.GetRequiredService<ILogger<Program>>();

        try
        {
            // Debug: Show current working directory and paths
            var currentDir = Directory.GetCurrentDirectory();
            logger.LogInformation("Current working directory: {CurrentDir}", currentDir);
            logger.LogInformation("Source path provided: {SourcePath}", options.SourceLocalesPath);
            logger.LogInformation("Target path provided: {TargetPath}", options.TargetLocalesPath);

            // Get the project root directory (where the .csproj file is located)
            var projectRoot = GetProjectRootDirectory();
            logger.LogInformation("Project root directory: {ProjectRoot}", projectRoot);

            // Convert relative paths to absolute paths based on project root
            var sourcePath = Path.IsPathRooted(options.SourceLocalesPath)
                ? options.SourceLocalesPath
                : Path.GetFullPath(Path.Combine(projectRoot, options.SourceLocalesPath));

            var targetPath = Path.IsPathRooted(options.TargetLocalesPath)
                ? options.TargetLocalesPath
                : Path.GetFullPath(Path.Combine(projectRoot, options.TargetLocalesPath));

            logger.LogInformation("Resolved source path: {SourcePath}", sourcePath);
            logger.LogInformation("Resolved target path: {TargetPath}", targetPath);

            // Validate paths
            if (!Directory.Exists(sourcePath))
            {
                logger.LogError("Source directory does not exist: {SourcePath}", sourcePath);
                Environment.Exit(1);
                return;
            }

            // Update options with resolved paths
            options.SourceLocalesPath = sourcePath;
            options.TargetLocalesPath = targetPath;

            // Configure Claude service
            var claudeService = serviceProvider.GetRequiredService<ClaudeTranslationService>();
            claudeService.ConfigureApiKey(options.ClaudeApiKey);
            claudeService.ConfigureDebugMode(debug);
            claudeService.ConfigureMaxTokens(options.MaxOutputTokens);

            // Configure debug mode if enabled
            if (debug)
            {
                logger.LogInformation("Debug mode enabled - problematic responses will be saved to debug files");
            }

            // Log configuration
            logger.LogInformation("""
                Translation Configuration:
                - Source: {SourcePath}
                - Target: {TargetPath}
                - Language: {TargetLanguage}
                - Debug Mode: {DebugMode}
                """,
                options.SourceLocalesPath,
                options.TargetLocalesPath,
                options.TargetLanguage,
                debug);

            // Process files
            var fileProcessor = serviceProvider.GetRequiredService<FileProcessingService>();
            var results = await fileProcessor.ProcessFilesAsync(options);

            // Exit with appropriate code
            Environment.Exit(results.FailedFiles > 0 ? 1 : 0);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Unexpected error during translation process");
            Environment.Exit(1);
        }
    }

    static async Task CheckTranslationStatusAsync(TranslationOptions options, IConfiguration config)
    {
        // Setup DI container
        var services = new ServiceCollection();

        // Configure logging
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.SetMinimumLevel(LogLevel.Information);
        });

        // Register services (no translation service needed for status check)
        services.AddSingleton<ClaudeTranslationService?>(provider => null);
        services.AddSingleton<FileProcessingService>(provider =>
            new FileProcessingService(
                provider.GetService<ClaudeTranslationService>(),
                provider.GetRequiredService<ILogger<FileProcessingService>>()));

        // Build service provider
        using var serviceProvider = services.BuildServiceProvider();
        var logger = serviceProvider.GetRequiredService<ILogger<Program>>();

        try
        {
            // Get the project root directory
            var projectRoot = GetProjectRootDirectory();
            logger.LogInformation("Project root directory: {ProjectRoot}", projectRoot);

            // Convert relative paths to absolute paths based on project root
            var sourcePath = Path.IsPathRooted(options.SourceLocalesPath)
                ? options.SourceLocalesPath
                : Path.GetFullPath(Path.Combine(projectRoot, options.SourceLocalesPath));

            var targetPath = Path.IsPathRooted(options.TargetLocalesPath)
                ? options.TargetLocalesPath
                : Path.GetFullPath(Path.Combine(projectRoot, options.TargetLocalesPath));

            logger.LogInformation("Resolved source path: {SourcePath}", sourcePath);
            logger.LogInformation("Resolved target path: {TargetPath}", targetPath);

            // Validate paths
            if (!Directory.Exists(sourcePath))
            {
                logger.LogError("Source directory does not exist: {SourcePath}", sourcePath);
                Environment.Exit(1);
                return;
            }

            // Update options with resolved paths
            options.SourceLocalesPath = sourcePath;
            options.TargetLocalesPath = targetPath;

            // Process files
            var fileProcessor = serviceProvider.GetRequiredService<FileProcessingService>();
            var status = await fileProcessor.CheckTranslationStatusAsync(options);

            // Display results
            Console.WriteLine();
            Console.WriteLine("=== Translation Status Report ===");
            Console.WriteLine($"Language: {options.TargetLanguage}");
            Console.WriteLine($"Source: {options.SourceLocalesPath}");
            Console.WriteLine($"Target: {options.TargetLocalesPath}");
            Console.WriteLine();

            Console.WriteLine($"Total files in source: {status.TotalSourceFiles}");
            Console.WriteLine($"Total files in target: {status.TotalTargetFiles}");
            Console.WriteLine($"Missing files: {status.MissingFiles.Count}");
            Console.WriteLine($"Extra files in target: {status.ExtraFiles.Count}");
            Console.WriteLine($"Files with key differences: {status.FilesWithKeyDifferences.Count}");
            Console.WriteLine($"Total missing keys: {status.TotalMissingKeys}");
            Console.WriteLine($"Total extra keys: {status.TotalExtraKeys}");
            Console.WriteLine();

            if (status.MissingFiles.Count > 0)
            {
                Console.WriteLine("=== Missing Files ===");
                foreach (var missingFile in status.MissingFiles.OrderBy(f => f))
                {
                    Console.WriteLine($"  ❌ {missingFile}");
                }
                Console.WriteLine();
            }

            if (status.ExtraFiles.Count > 0)
            {
                Console.WriteLine("=== Extra Files in Target ===");
                foreach (var extraFile in status.ExtraFiles.OrderBy(f => f))
                {
                    Console.WriteLine($"  ⚠️  {extraFile}");
                }
                Console.WriteLine();
            }

            if (status.FilesWithKeyDifferences.Count > 0)
            {
                Console.WriteLine("=== Files with Key Differences ===");
                foreach (var fileDiff in status.FilesWithKeyDifferences.OrderBy(f => f.FilePath))
                {
                    Console.WriteLine($"  🔍 {fileDiff.FilePath}");

                    if (fileDiff.MissingKeys.Count > 0)
                    {
                        Console.WriteLine($"    Missing keys ({fileDiff.MissingKeys.Count}):");
                        foreach (var key in fileDiff.MissingKeys.Take(5)) // Show first 5 missing keys
                        {
                            Console.WriteLine($"      - {key}");
                        }
                        if (fileDiff.MissingKeys.Count > 5)
                        {
                            Console.WriteLine($"      ... and {fileDiff.MissingKeys.Count - 5} more");
                        }
                    }

                    if (fileDiff.ExtraKeys.Count > 0)
                    {
                        Console.WriteLine($"    Extra keys ({fileDiff.ExtraKeys.Count}):");
                        foreach (var key in fileDiff.ExtraKeys.Take(5)) // Show first 5 extra keys
                        {
                            Console.WriteLine($"      + {key}");
                        }
                        if (fileDiff.ExtraKeys.Count > 5)
                        {
                            Console.WriteLine($"      ... and {fileDiff.ExtraKeys.Count - 5} more");
                        }
                    }
                    Console.WriteLine();
                }
            }

            if (status.MissingFiles.Count == 0 && status.ExtraFiles.Count == 0 && status.FilesWithKeyDifferences.Count == 0)
            {
                Console.WriteLine("✅ All files are synchronized!");
            }
            else
            {
                var totalIssues = status.MissingFiles.Count + status.ExtraFiles.Count + status.FilesWithKeyDifferences.Count;
                Console.WriteLine($"📊 Translation Progress: {status.TotalTargetFiles}/{status.TotalSourceFiles} files ({status.TotalTargetFiles * 100.0 / status.TotalSourceFiles:F1}%)");
                Console.WriteLine($"⚠️  Issues found: {totalIssues} file-level issues, {status.TotalMissingKeys} missing keys, {status.TotalExtraKeys} extra keys");
            }

            // Exit with appropriate code
            Environment.Exit(status.MissingFiles.Count > 0 ? 1 : 0);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Unexpected error during status check");
            Environment.Exit(1);
        }
    }

    static string GetProjectRootDirectory()
    {
        // Start from the current directory and walk up until we find the project root
        var currentDir = Directory.GetCurrentDirectory();

        // Look for the project root by finding the directory that contains the .csproj file
        while (currentDir != null)
        {
            if (File.Exists(Path.Combine(currentDir, "LocaleTranslator.csproj")))
            {
                return currentDir;
            }

            var parentDir = Directory.GetParent(currentDir);
            if (parentDir == null)
            {
                break;
            }
            currentDir = parentDir.FullName;
        }

        // Fallback: return the current directory if we can't find the project root
        return Directory.GetCurrentDirectory();
    }
}