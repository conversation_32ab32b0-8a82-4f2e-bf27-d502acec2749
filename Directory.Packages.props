﻿
<Project>
  <PropertyGroup>
    <!-- Enable Central Package Management for entire solution -->
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
    <CentralPackageTransitivePinningEnabled>true</CentralPackageTransitivePinningEnabled>
    
    <!-- Disable packaging by default for generated applications -->
    <!-- Override in Directory.Packages.Custom.props for specific projects that need packaging -->
    <GeneratePackageOnBuild>false</GeneratePackageOnBuild>
    <IsPackable>false</IsPackable>
    <PublishToLocalFeed>false</PublishToLocalFeed>
  </PropertyGroup>

  <!-- Generative Objects Version Parameters -->
  <PropertyGroup>
    <!-- Framework Package Versions -->
    <GOFrameworkVersion>2.5.1</GOFrameworkVersion>
    <!-- Modeler Package Versions -->
    <GOModelerVersion>2.6.0</GOModelerVersion>
  </PropertyGroup>
  
  <!-- All External Package Versions - Used by both Framework and Application projects -->
  <ItemGroup>

    
    <!-- Microsoft Core Packages -->
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.5.0" />
    <PackageVersion Include="Microsoft.AspNetCore.App" Version="8.0.0" />
    <PackageVersion Include="Microsoft.AspNetCore.DataProtection" Version="8.0.0" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore" Version="7.0.0" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.SqlServer" Version="7.0.0" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Tools" Version="7.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.FileExtensions" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Hosting.Abstractions" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.FileProviders.Physical" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.FileSystemGlobbing" Version="8.0.0" />
    
    <!-- NHibernate ORM -->
    <PackageVersion Include="NHibernate" Version="5.5.2" />
    <PackageVersion Include="NHibernate.Mapping.Attributes" Version="5.4.0" />
    <PackageVersion Include="FluentNHibernate" Version="3.1.0" />
    
    <!-- Database Providers -->
    <PackageVersion Include="System.Data.SqlClient" Version="4.9.0" />
    <PackageVersion Include="Microsoft.Data.SqlClient" Version="5.2.1" />
    <PackageVersion Include="MySql.Data" Version="8.1.0" />
    <PackageVersion Include="Oracle.ManagedDataAccess.Core" Version="3.21.120" />
    <PackageVersion Include="Devart.Data.MySql" Version="9.1.134" />
    <PackageVersion Include="Microsoft.SqlServer.SqlManagementObjects" Version="170.18.0" />
    <PackageVersion Include="System.Drawing.Common" Version="9.0.8"/>

    <!-- JSON and Serialization -->
    <PackageVersion Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageVersion Include="System.Text.Json" Version="8.0.0" />
    <PackageVersion Include="System.Text.Encodings.Web" Version="8.0.0" />
    
    <!-- Authentication and Security -->
    <PackageVersion Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="7.0.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="7.0.0" />
    <PackageVersion Include="System.IdentityModel.Tokens.Jwt" Version="6.35.0" />
    <PackageVersion Include="IdentityModel" Version="7.0.0" />
    
    <!-- ASP.NET Core -->
    <PackageVersion Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="7.0.5" />
    <PackageVersion Include="Microsoft.AspNetCore.Http.Features" Version="5.0.17" />
    
    <!-- Azure Storage (modern packages replacing WindowsAzure.Storage) -->
    <PackageVersion Include="Azure.Storage.Blobs" Version="12.19.1" />
    <PackageVersion Include="Azure.Storage.Common" Version="12.18.1" />
    <PackageVersion Include="Azure.Storage.Files.Shares" Version="12.17.1" />
    <PackageVersion Include="Azure.Storage.Queues" Version="12.17.1" />
    <PackageVersion Include="Microsoft.Azure.Management.Fluent" Version="1.38.1" />
    
    <!-- Testing Framework -->
    <PackageVersion Include="xunit" Version="2.4.2" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="2.4.5" />
    <PackageVersion Include="Moq" Version="4.20.69" />
    <PackageVersion Include="FluentAssertions" Version="6.12.0" />
    <PackageVersion Include="NFluent" Version="2.8.0" />
    <PackageVersion Include="NSubstitute" Version="5.1.0" />
    <PackageVersion Include="NUnit" Version="3.13.3" />
    <PackageVersion Include="NUnit.Analyzers" Version="3.10.0" />
    <PackageVersion Include="NUnit3TestAdapter" Version="4.4.2" />
    <PackageVersion Include="coverlet.collector" Version="6.0.0" />
    <PackageVersion Include="Castle.Core" Version="5.1.1" />
    <PackageVersion Include="NuGet.Frameworks" Version="5.11.0" />
    
    <!-- Selenium Testing -->
    <PackageVersion Include="Selenium.Support" Version="4.7.0" />
    <PackageVersion Include="DotNetSeleniumExtras.WaitHelpers" Version="3.11.0" />
    
    <!-- Build and Code Analysis -->
    <PackageVersion Include="Microsoft.CodeAnalysis.Analyzers" Version="3.11.0" />
    <PackageVersion Include="Microsoft.CodeAnalysis.NetAnalyzers" Version="7.0.4" />
    <PackageVersion Include="SonarAnalyzer.CSharp" Version="9.8.0.76515" />
    <PackageVersion Include="Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers" Version="0.4.410601" />
    
    <!-- Source Link -->
    <PackageVersion Include="Microsoft.SourceLink.GitHub" Version="8.0.0" />
    
    <!-- Performance and Profiling -->
    <PackageVersion Include="MiniProfiler.AspNetCore.Mvc" Version="4.3.8" />
    <PackageVersion Include="BenchmarkDotNet" Version="0.13.7" />
    
    <!-- Utilities -->
    <PackageVersion Include="AutoMapper" Version="12.0.1" />
    <PackageVersion Include="Serilog" Version="3.0.1" />
    <PackageVersion Include="Serilog.AspNetCore" Version="7.0.0" />
    <PackageVersion Include="Polly" Version="7.2.4" />
    <PackageVersion Include="ClosedXML" Version="0.101.0" />
    <PackageVersion Include="dotNetRDF" Version="2.7.5" />
    <PackageVersion Include="PuppeteerSharp" Version="18.0.5" />
    <PackageVersion Include="ExcelDataReader" Version="3.6.0" />
    <PackageVersion Include="MailKit" Version="3.4.3" />
    
    <!-- Dependency Injection -->
    <PackageVersion Include="Unity.Container" Version="5.11.11" />
    <PackageVersion Include="Unity.Microsoft.DependencyInjection" Version="5.11.5" />
    
    <!-- System and Runtime -->
    <PackageVersion Include="System.ComponentModel.Annotations" Version="5.0.0" />
    <PackageVersion Include="System.Threading.Tasks.Extensions" Version="4.5.4" />
    <PackageVersion Include="System.Runtime.CompilerServices.Unsafe" Version="6.0.0" />
    <PackageVersion Include="System.CodeDom" Version="7.0.0" />
    <PackageVersion Include="System.Configuration.ConfigurationManager" Version="8.0.0" />
    <PackageVersion Include="System.Data.DataSetExtensions" Version="4.5.0" />
    <PackageVersion Include="System.IO.Pipelines" Version="7.0.0" />
    <PackageVersion Include="Microsoft.Windows.Compatibility" Version="7.0.0" />
    <PackageVersion Include="System.Diagnostics.DiagnosticSource" Version="8.0.0" />
    <PackageVersion Include="System.Reflection.Metadata" Version="9.0.0" />
    
    <!-- Force specific versions to avoid PCL conflicts -->
    <PackageVersion Include="Microsoft.NETCore.Portable.Compatibility" Version="1.0.1" />

    <PackageVersion Include="System.Net.Http" Version="4.3.4" />
    <PackageVersion Include="System.Runtime" Version="4.3.1" />
    <PackageVersion Include="System.IO" Version="4.3.0" />
    <PackageVersion Include="System.Threading.Tasks" Version="4.3.0" />
    <PackageVersion Include="System.Security.Cryptography.Algorithms" Version="4.3.1" />
    <PackageVersion Include="System.Security.Cryptography.X509Certificates" Version="4.3.2" />
    
    <!-- Additional packages to fix PCL framework issues -->
    <PackageVersion Include="Microsoft.Bcl.Build" Version="1.0.21" />
    <PackageVersion Include="Microsoft.Bcl" Version="1.1.10" />
    <PackageVersion Include="Microsoft.Net.Http" Version="2.2.29" />
    
    <!-- Missing packages causing build errors -->
    <PackageVersion Include="ReflectionBridge" Version="0.0.8" />
    <PackageVersion Include="JetBrains.Annotations" Version="2023.3.0" />
    <PackageVersion Include="I18Next.Net" Version="1.0.0" />
    <PackageVersion Include="I18Next.Net.Extensions" Version="1.0.0" />
    <PackageVersion Include="System.Linq.Dynamic.Core" Version="1.3.7" />
    <PackageVersion Include="Microsoft.CSharp" Version="4.7.0" />
    <PackageVersion Include="Microsoft.AspNet.WebApi.Client" Version="6.0.0" />
    <PackageVersion Include="Microsoft.Web.Administration" Version="11.1.0" />
    <PackageVersion Include="Microsoft.Build.Utilities.Core" Version="17.9.5" />
    <PackageVersion Include="System.Data.Odbc" Version="7.0.0" />
    <PackageVersion Include="System.Data.OleDb" Version="7.0.0" />
    <PackageVersion Include="NLog" Version="5.2.8" />
    <PackageVersion Include="QuickGraph" Version="3.6.61119.7" />
    
    <!-- ANTLR Packages -->
    <PackageVersion Include="Antlr4.Runtime" Version="4.6.6" />
    <PackageVersion Include="Antlr4.CodeGenerator" Version="4.6.6" />
    
    <!-- Swagger/OpenAPI -->
    <PackageVersion Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    <PackageVersion Include="Swashbuckle.AspNetCore.Annotations" Version="6.5.0" />
    <PackageVersion Include="Swashbuckle.AspNetCore.Newtonsoft" Version="6.5.0" />
    
    
    <!-- GO Framework Packages - Generation Models EXPLICITLY target specific Framework version -->
    <!-- IMPORTANT: targetFrameworkVersion controls what Framework the templates generate code for -->
    <!-- When Framework major version changes, deliberately update targetFrameworkVersion and Generation Models version -->
    <PackageVersion Include="GenerativeObjects.Practices" Version="$(GOFrameworkVersion)" />
    <PackageVersion Include="GenerativeObjects.Services" Version="$(GOFrameworkVersion)" />
    <PackageVersion Include="GenerativeObjects.Infrastructure" Version="$(GOFrameworkVersion)" />
    <PackageVersion Include="GenerativeObjects.Infrastructure.Database" Version="$(GOFrameworkVersion)" />
    <PackageVersion Include="GenerativeObjects.Infrastructure.Database.Portal" Version="$(GOFrameworkVersion)" />
    <PackageVersion Include="GenerativeObjects.Infrastructure.Database.Azure" Version="$(GOFrameworkVersion)" />
    <PackageVersion Include="GenerativeObjects.Infrastructure.Database.SqlServer" Version="$(GOFrameworkVersion)" />
    <PackageVersion Include="GenerativeObjects.Practices.LayerSupportClasses" Version="$(GOFrameworkVersion)" />
    <PackageVersion Include="GenerativeObjects.Practices.ORMSupportClasses" Version="$(GOFrameworkVersion)" />
    <PackageVersion Include="GenerativeObjects.Practices.SharePoint" Version="$(GOFrameworkVersion)" />
    <PackageVersion Include="GenerativeObjects.Practices.Test" Version="$(GOFrameworkVersion)" />
    <PackageVersion Include="GenerativeObjects.DSL.ValueReference" Version="$(GOFrameworkVersion)" />
    
    <!-- Feature Provider Packages - Target same Modeler version as Generation Models -->
    <PackageVersion Include="GenerativeObjects.Features.FeatureProviderHelper" Version="$(GOModelerVersion)" />
    <PackageVersion Include="GenerativeObjects.Features.Security" Version="$(GOModelerVersion)" />
    <PackageVersion Include="GenerativeObjects.Features.ChangeTracking" Version="$(GOModelerVersion)" />
    <PackageVersion Include="GenerativeObjects.Features.RemoteExtensions" Version="$(GOModelerVersion)" />
    <PackageVersion Include="GenerativeObjects.Features.FeatureProvider.ChangeTracking" Version="$(GOModelerVersion)" />
    <PackageVersion Include="GenerativeObjects.Features.FeatureProvider.ElasticSearch" Version="$(GOModelerVersion)" />
    
  </ItemGroup>
</Project> 
