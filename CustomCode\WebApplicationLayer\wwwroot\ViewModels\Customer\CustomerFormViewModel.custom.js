﻿(function () {
    //
    FleetXQ.Web.ViewModels.CustomerFormViewModelCustom = function (viewmodel) {
        var self = this;
        this.viewmodel = viewmodel;

        // Sites visible only if : no access group defined or : Access group defined and user has access to customers and to view customer site
        this.IsSitesVisible = function () {
            return ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.HAS_CUSTOMERS_ACCESS) &&
                ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_VIEW_SITE);
        };

        this.initialize = function () {
            console.log('CustomerFormViewModel initialize called');

            // Check if we should enter edit mode
            if (sessionStorage.getItem('enterEditMode') === 'true') {
                console.log('Edit mode flag found, attempting to enter edit mode');
                setTimeout(function () {
                    var editButton = document.querySelector('[data-test-id="6c4d6842-e8a6-4546-a5ee-ff5dbc501ab8"]');
                    if (editButton) {
                        console.log('Edit button found, clicking');
                        editButton.click();
                        // Clear the flags after clicking
                        sessionStorage.removeItem('enterEditMode');
                        sessionStorage.removeItem('customerId');
                    } else {
                        console.log('Edit button not found');
                    }
                }, 1000);
            }

            // // Temporary Fix for Context related issues
            // if (!sessionStorage.getItem('hasReloaded')) {
            //     // Set the flag before reloading
            //     sessionStorage.setItem('hasReloaded', 'true');

            //     // Force a reload after a brief delay to ensure hash is set
            //     window.location.reload();
            // } else {
            //     // Clear the flag for next time
            //     sessionStorage.removeItem('hasReloaded');
            // }

            self.viewmodel.CustomerModelItemsGridViewModel.customerId = ko.observable(null);
            self.viewmodel.CustomerModelItemsGridViewModel.dealerId = ko.observable(null);

            self.viewmodel.subscriptions.push(self.viewmodel.CurrentObject.subscribe(function () {
                self.viewmodel.CustomerModelItemsGridViewModel.customerId(self.viewmodel.CustomerObject().Data.Id());
                self.viewmodel.CustomerModelItemsGridViewModel.dealerId(self.viewmodel.CustomerObject().Data.DealerId());
            }));

            self.viewmodel.subscriptions.push(self.viewmodel.Events.CustomerLoaded.subscribe(function (newValue) {
                self.viewmodel.StatusData.IsSiteTabVisible(ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.HAS_CUSTOMERS_ACCESS) &&
                    ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_VIEW_SITE));
                self.viewmodel.StatusData.IsEMAILSTabVisible(ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.HAS_CUSTOMERS_ACCESS) &&
                    ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_VIEW_EMAIL_GROUP));
                self.viewmodel.StatusData.IsMODELSTabVisible(ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.HAS_CUSTOMERS_ACCESS) &&
                    ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_VIEW_MODELS));
                self.viewmodel.StatusData.IsACCESSGROUPSTabVisible(ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.HAS_CUSTOMERS_ACCESS) &&
                    ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_VIEW_ACCESS_GROUPS));
            }));
        }

    }
}());
