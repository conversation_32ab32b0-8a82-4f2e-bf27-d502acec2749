(function () {
    FleetXQ.Web.ViewModels.VehicleFormViewModelCustom = function (viewmodel) {
        var self = this;
        this.viewmodel = viewmodel;

        self.IsCreateNewCommandVisible = ko.pureComputed(function () {
            return false;
        });

        this.onAfterSave = function () {
            // Check if VORStatus has changed in VehicleOtherSettings
            var vehicleOtherSettings = self.viewmodel.VehicleOtherSettingsFormViewModel;
            if (vehicleOtherSettings && vehicleOtherSettings.SavedData &&
                vehicleOtherSettings.SavedData.Data.VORStatus() !== vehicleOtherSettings.VehicleOtherSettingsObject().Data.VORStatus()) {
                // Show popup message
                self.viewmodel.controller.applicationController.showAlertPopup(
                    self.viewmodel,
                    "The VOR setting will now be synchronized with the vehicle. Once the vehicle receives the VOR mode, the value in the dashboard will reflect the changes.",
                    "VOR Status Update",
                    null,
                    self.viewmodel.contextId
                );
            }

            var vehicleId = self.viewmodel.CurrentObject().Data.Id();

            if (vehicleId && vehicleId !== 'null') {
                // Subscribe to the VehicleSaved event
                self.viewmodel.Events.VehicleSaved.subscribe(function (savedVehicle) {
                    if (savedVehicle && savedVehicle.Data && savedVehicle.Data.Id() === vehicleId) {
                        // Vehicle is fully saved and loaded, safe to navigate
                        self.viewmodel.controller.applicationController.customNavigateToVehicleDetail(vehicleId);
                    }
                });
            } else {
                console.warn('No valid vehicle ID available for redirect');
            }

            self.viewmodel.CurrentObject().Data.IsNew(false);
            self.updateTabVisibility();
            self.viewmodel.resetValidation();
            return true;
        }

        this.IsModifyCommandVisible = function () {
            return (self.viewmodel.StatusData.DisplayMode() == 'view' && !self.viewmodel.StatusData.IsEmpty() && self.viewmodel.DataStore && self.viewmodel.DataStore.CheckAuthorizationForEntityAndMethod('save'))
                && (ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.HAS_VEHICLES_ACCESS) &&
                    ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_EDIT_VEHICLE));
        };

        this.updateTabVisibility = function () {
            if (self.viewmodel.CurrentObject().Data.IsNew()) {
                self.viewmodel.StatusData.IsCHECKLISTTabVisible(false);
                self.viewmodel.StatusData.IsIMPACTSETTINGSTabVisible(false);
                self.viewmodel.StatusData.IsSERVICETabVisible(false);
                self.viewmodel.StatusData.IsMORESETTINGSTabVisible(false);

                return;
            }

            self.viewmodel.StatusData.IsCHECKLISTTabVisible(ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.HAS_VEHICLES_ACCESS) &&
                ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_VIEW_CHECKLIST) &&
                ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_VIEW_CHECKLIST_SETTING));
            self.viewmodel.StatusData.IsIMPACTSETTINGSTabVisible(ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.HAS_VEHICLES_ACCESS) &&
                ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_VIEW_IMPACT_SETTING));
            self.viewmodel.StatusData.IsSERVICETabVisible(ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.HAS_VEHICLES_ACCESS) &&
                ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_VIEW_SERVICE));
            self.viewmodel.StatusData.IsMORESETTINGSTabVisible(ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.HAS_VEHICLES_ACCESS) &&
                (ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_VIEW_VOR_STATUS) ||
                    ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_VIEW_FULL_IMPACT_LOCKOUT)));
        }

        this.initialize = function () {
            // Temporary Fix for Context related issues
            // if (!sessionStorage.getItem('hasReloaded')) {
            //     // Set the flag before reloading
            //     sessionStorage.setItem('hasReloaded', 'true');

            //     // Force a reload after a brief delay to ensure hash is set
            //     window.location.reload();
            // } else {
            //     // Clear the flag for next time
            //     sessionStorage.removeItem('hasReloaded');
            // }

            self.updateTabVisibility();
            self.viewmodel.Commands.IsSendQuestionsToVehicleCommandVisible = ko.observable(false);

            self.viewmodel.subscriptions.push(self.viewmodel.Events.CancelEdit.subscribe(function (newValue) {
                if (self.viewmodel.CurrentObject().Data.IsNew()) {
                    window.location.hash = "!/Vehicles";
                }
            }));

            self.viewmodel.subscriptions.push(self.viewmodel.Events.VehicleLoaded.subscribe(function (newValue) {

                if (self.viewmodel.CurrentObject().Data.ChecklistSettings()) {
                    self.viewmodel.Commands.IsSendQuestionsToVehicleCommandVisible(true);
                }
                else {
                    self.viewmodel.Commands.IsSendQuestionsToVehicleCommandVisible(false);
                }

                self.updateTabVisibility();
            }));

            self.viewmodel.subscriptions.push(self.viewmodel.Events.VehicleSaved.subscribe(function (newValue) {

                if (self.viewmodel.CurrentObject().Data.ChecklistSettings()) {
                    self.viewmodel.Commands.IsSendQuestionsToVehicleCommandVisible(true);
                }
                else {
                    self.viewmodel.Commands.IsSendQuestionsToVehicleCommandVisible(false);
                }
            }));

            self.viewmodel.Commands.IsSyncCommandVisible = function () {
                return ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.HAS_VEHICLES_ACCESS) &&
                    ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_VIEW_SYNCHRONIZATION) &&
                    !ApplicationController.viewModel.security.currentUserClaims().role?.includes('Customer');
            }

            self.viewmodel.isDeleteServiceSettings1Visible = function () {
                return false;
            };

            self.viewmodel.isDeleteServiceSettingsVisible = function () {
                return false;
            };
            self.viewmodel.isDeleteInspectionVisible = function () {
                return false;
            };

            self.viewmodel.isCreateNewServiceSettingsVisible = function () {
                return ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.HAS_VEHICLES_ACCESS) &&
                    ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_CREATE_SERVICE);
            };
            self.viewmodel.isCreateNewServiceSettings1Visible = function () {
                return ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.HAS_VEHICLES_ACCESS) &&
                    ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_CREATE_SERVICE);
            };


            // Add onConfirmDelete function
            this.onConfirmDelete = function (confirm) {
                if (confirm === true) {
                    var configuration = {};
                    configuration.caller = self.viewmodel;
                    configuration.contextId = self.viewmodel.contextId;
                    configuration.successHandler = function (data) {
                        self.viewmodel.onDeleteSuccess(data);
                        // Close the form after successful deletion
                        self.viewmodel.closePopup(true);
                        // Navigate to vehicles page
                        window.location.hash = "!/Vehicles";
                    };
                    configuration.errorHandler = self.viewmodel.ShowError;
                    configuration.vehicleId = self.viewmodel.VehicleObject().Data.Id();
                    self.viewmodel.setIsBusy(true);
                    self.viewmodel.controller.applicationController.getProxyForComponent("VehicleAPI").SoftDelete(configuration);
                } else {
                    self.viewmodel.setIsBusy(false);
                }
            };

            // Override the Delete function to show confirmation popup
            this.viewmodel.Delete = function () {
                self.viewmodel.setIsBusy(true);
                self.viewmodel.controller.applicationController.showConfirmPopup(
                    self.viewmodel,
                    FleetXQ.Web.Messages.i18n.t('dataSource.confirmDeleteMessage', { entity: FleetXQ.Web.Messages.i18n.t('entities/Vehicle/Vehicle:entityName') }),
                    FleetXQ.Web.Messages.i18n.t('dataSource.confirmDeletePopupTitle'),
                    self.onConfirmDelete,
                    self.viewmodel.contextId
                );
            };
        }
    }
}());