﻿(function () {
    //
    FleetXQ.Web.ViewModels.CustomerGridViewModelCustom = function (viewmodel) {
        var self = this;
        this.viewmodel = viewmodel;

        this.initialize = function () {
            // Set base filter to exclude deleted records
            self.viewmodel.baseFilterPredicate = "DeletedAtUtc == null";
            self.viewmodel.baseFilterParameters = null;
            self.viewmodel.baseFilterParametersCount = 0;

            // // Temporary Fix for Context related issues
            // if (!sessionStorage.getItem('hasReloaded')) {
            //     // Set the flag before reloading
            //     sessionStorage.setItem('hasReloaded', 'true');

            //     // Force a reload after a brief delay to ensure hash is set
            //     window.location.reload();
            // } else {
            //     // Clear the flag for next time
            //     sessionStorage.removeItem('hasReloaded');
            // }

            // Add delete command to viewmodel commands
            self.viewmodel.commands.DeleteCustomerCommand = function (data) {
                self.viewmodel.setIsBusy(true);
                ApplicationController.showConfirmPopup(
                    self.viewmodel,
                    FleetXQ.Web.Messages.i18n.t('dataSource.confirmDeleteMessage', { entity: FleetXQ.Web.Messages.i18n.t('entities/Customer/Customer:entityName') }),
                    FleetXQ.Web.Messages.i18n.t('dataSource.confirmDeletePopupTitle'),
                    function (confirm) { self.viewmodel.onConfirmDelete(confirm, data); },
                    self.viewmodel.contextId
                );
            };

            self.viewmodel.onConfirmDelete = function (confirm, data) {
                if (confirm === true) {
                    var configuration = {
                        contextId: self.viewmodel.contextId,
                        successHandler: function () {
                            self.viewmodel.Rebind(true);
                            self.viewmodel.setIsBusy(false);
                        },
                        errorHandler: function (error) {
                            self.viewmodel.ShowError(error);
                            self.viewmodel.setIsBusy(false);
                        },
                        customerId: data.Data.Id()
                    };

                    self.viewmodel.setIsBusy(true);
                    self.viewmodel.controller.applicationController.getProxyForComponent("CustomerAPI").SoftDelete(configuration);
                } else {
                    self.viewmodel.setIsBusy(false);
                }
            };

            // Add command visibility and enable states
            self.viewmodel.commands.IsDeleteCustomerCommandVisible = function () {
                return true;
            };

            self.viewmodel.commands.IsDeleteCustomerCommandEnabled = function () {
                return true;
            };
        }
    }
}());
